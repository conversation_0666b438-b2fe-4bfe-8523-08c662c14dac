{"applications": "*", "changelogDate": "20220824183042", "dto": "mapstruct", "embedded": false, "entityTableName": "district", "fields": [{"fieldName": "districtName", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "deleted", "fieldType": "Boolean"}, {"fieldName": "lgdCode", "fieldType": "<PERSON>"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "District", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceClass"}