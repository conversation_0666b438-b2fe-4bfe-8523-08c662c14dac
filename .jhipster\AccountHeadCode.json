{"applications": "*", "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "account_head_code", "fields": [{"fieldName": "type", "fieldType": "MappingType", "fieldValues": "HEADOFFICE (HeadOffice),SHARE (Share),MEMBER (Member),LOAN (Loan),SUNDRY (Sundry),PURCHASE (Purchase),SALES (Sales),LOANPRODUCT (LoanProduct),DEPOSIT (Deposit),BORROWING (Borrowing),INVESTMENT (Investment)"}, {"fieldName": "value", "fieldType": "String"}, {"fieldName": "headCodeName", "fieldType": "String"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "AccountHeadCode", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "ledgerAccounts", "relationshipName": "ledgerAccounts", "relationshipType": "many-to-one"}], "service": "serviceClass"}