{"applications": "*", "changelogDate": "20220824183108", "dto": "mapstruct", "embedded": false, "entityTableName": "nominee", "fields": [{"fieldName": "firstName", "fieldType": "String"}, {"fieldName": "lastName", "fieldType": "String"}, {"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "guardianName", "fieldType": "String"}, {"fieldName": "gender", "fieldType": "String"}, {"fieldName": "dob", "fieldType": "LocalDate"}, {"fieldName": "aadharCard", "fieldType": "String", "fieldValidateRules": ["unique"]}, {"fieldName": "nominee<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "relation", "fieldType": "String"}, {"fieldName": "permanentAddr", "fieldType": "String"}, {"fieldName": "nomineePer<PERSON><PERSON>", "fieldType": "Double"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isActive", "fieldType": "Boolean"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}, {"fieldName": "freeField4", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "<PERSON><PERSON><PERSON>", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "member", "relationshipName": "member", "relationshipType": "many-to-one"}], "service": "serviceClass"}