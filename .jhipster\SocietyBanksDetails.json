{"applications": "*", "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "society_banks_details", "fields": [{"fieldName": "bankName", "fieldType": "String"}, {"fieldName": "ifsccode", "fieldType": "String"}, {"fieldName": "branchName", "fieldType": "String"}, {"fieldName": "accountNumber", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "isActive", "fieldType": "Boolean"}, {"fieldName": "accountType", "fieldType": "String"}, {"fieldName": "accHeadCode", "fieldType": "String"}, {"fieldName": "parentAccHeadCode", "fieldType": "String"}, {"fieldName": "accountName", "fieldType": "String"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}, {"fieldName": "freeField4", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "SocietyBanksDetails", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "society", "relationshipName": "society", "relationshipType": "many-to-one"}], "service": "serviceClass"}