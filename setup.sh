#!/bin/bash

# PACS Backend Docker Setup Script
# This script sets up the directory structure and permissions for the PACS backend application

set -e

echo "Setting up PACS Backend Docker environment..."

# Create directory structure
echo "Creating directory structure at /srv/pacs..."
sudo mkdir -p /srv/pacs/{mysql/{data,config,logs},uploads,logs}

# Set proper ownership and permissions
echo "Setting permissions..."
sudo chown -R 1000:1000 /srv/pacs/uploads
sudo chown -R 1000:1000 /srv/pacs/logs
sudo chown -R 999:999 /srv/pacs/mysql
sudo chmod -R 755 /srv/pacs

# Create MySQL configuration file
echo "Creating MySQL configuration..."
sudo tee /srv/pacs/mysql/config/my.cnf > /dev/null <<EOF
[mysqld]
# Basic settings
default-storage-engine=innodb
innodb_file_per_table=1
max_connections=200
max_allowed_packet=256M

# Character set
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# Logging
log-error=/var/log/mysql/error.log
general-log=1
general-log-file=/var/log/mysql/general.log
slow-query-log=1
slow-query-log-file=/var/log/mysql/slow.log
long_query_time=2

# Performance tuning
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# Security
skip-name-resolve
EOF

# Create environment file template
echo "Creating environment file template..."
cat > .env.example <<EOF
# PACS Backend Environment Variables
# Copy this file to .env and modify the values as needed

# Database Configuration
MYSQL_DATABASE=pacs
MYSQL_USER=pacs
MYSQL_PASSWORD=pacsSecurePassword123!
MYSQL_ROOT_PASSWORD=rootSecurePassword123!

# Application Configuration
JAVA_OPTS=-Xmx2g -Xms1g
SPRING_PROFILES_ACTIVE=prod,api-docs

# Security (Change these in production!)
JHIPSTER_SECURITY_AUTHENTICATION_JWT_BASE64_SECRET=NmFlNTRlNjY5MGY0Y2UzMWE4ZmE0NzY1ZjVhNzNkOTA4MTllYzYxNDk3ZmM3M2U2M2Y4MzJmYWU0YWY5ZDRiZjhhZDc2MDYzZmQzNmE5MzZkYzE5ODI5MzEyMmIyMzYwYzNiYTZjMjczY2FlYzRkN2M0MmNhZGY5MDllNjg0MDY=

# Upload Configuration
UPLOAD_PATH=/app/uploads
EOF

echo "Creating deployment script..."
cat > deploy.sh <<EOF
#!/bin/bash

# PACS Backend Deployment Script

set -e

echo "Starting PACS Backend deployment..."

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Build the application image if Dockerfile exists
if [ -f "Dockerfile" ]; then
    echo "Building application image..."
    docker build -t pacsbackend:latest .
else
    echo "No Dockerfile found. Make sure to build the image using Maven Jib plugin:"
    echo "mvn clean package jib:dockerBuild"
fi

# Start the services
echo "Starting services..."
docker-compose up -d

# Wait for services to be healthy
echo "Waiting for services to be ready..."
sleep 30

# Check service status
echo "Checking service status..."
docker-compose ps

echo "Deployment completed!"
echo "Application should be available at: http://localhost:8191"
echo "Health check: http://localhost:8191/management/health"
echo ""
echo "To view logs:"
echo "  Application: docker-compose logs -f pacs-backend"
echo "  Database: docker-compose logs -f pacs-mysql"
echo ""
echo "To stop services: docker-compose down"
echo "To restart services: docker-compose restart"
EOF

chmod +x deploy.sh

echo "Creating backup script..."
cat > backup.sh <<EOF
#!/bin/bash

# PACS Backend Backup Script

set -e

BACKUP_DIR="/srv/pacs/backups"
DATE=\$(date +%Y%m%d_%H%M%S)

echo "Creating backup directory..."
sudo mkdir -p \$BACKUP_DIR

echo "Backing up MySQL database..."
docker-compose exec -T pacs-mysql mysqldump -u pacs -ppacsSecurePassword123! pacs > \$BACKUP_DIR/pacs_db_\$DATE.sql

echo "Backing up uploads..."
sudo tar -czf \$BACKUP_DIR/pacs_uploads_\$DATE.tar.gz -C /srv/pacs uploads

echo "Backup completed!"
echo "Database backup: \$BACKUP_DIR/pacs_db_\$DATE.sql"
echo "Uploads backup: \$BACKUP_DIR/pacs_uploads_\$DATE.tar.gz"
EOF

chmod +x backup.sh

echo ""
echo "Setup completed successfully!"
echo ""
echo "Directory structure created at /srv/pacs:"
echo "├── mysql/"
echo "│   ├── data/     (MySQL data files)"
echo "│   ├── config/   (MySQL configuration)"
echo "│   └── logs/     (MySQL logs)"
echo "├── uploads/      (Application uploads)"
echo "└── logs/         (Application logs)"
echo ""
echo "Next steps:"
echo "1. Build your application image:"
echo "   mvn clean package jib:dockerBuild"
echo ""
echo "2. Copy .env.example to .env and modify passwords:"
echo "   cp .env.example .env"
echo "   nano .env"
echo ""
echo "3. Deploy the application:"
echo "   ./deploy.sh"
echo ""
echo "4. Monitor the deployment:"
echo "   docker-compose logs -f"
echo ""
echo "Additional scripts created:"
echo "- deploy.sh: Deploy the application"
echo "- backup.sh: Backup database and uploads"
