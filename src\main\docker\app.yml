# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
version: '3.8'
services:
  pacsbackend-app:
    image: pacsbackend
    environment:
      - _JAVA_OPTIONS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED=true
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************************************************************
      - SPRING_LIQUIBASE_URL=********************************************************************************************************************************************************************************
      - JHIPSTER_SLEEP=30 # gives time for other services to boot before the application
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:8080:8080
  pacsbackend-mysql:
    image: mysql:8.0.29
    # volumes:
    #   - ~/volumes/jhipster/pacsBackend/mysql/:/var/lib/mysql/
    volumes:
      - ./config/mysql:/etc/mysql/conf.d
    environment:
      - MYSQL_ALLOW_EMPTY_PASSWORD=yes
      - MYSQL_DATABASE=pacsbackend
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:3306:3306
    command: mysqld --lower_case_table_names=1 --skip-ssl --character_set_server=utf8mb4 --explicit_defaults_for_timestamp
