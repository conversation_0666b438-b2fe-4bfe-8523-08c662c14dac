package com.techvg.pacs.config;

import java.time.Duration;
import org.ehcache.config.builders.*;
import org.ehcache.jsr107.Eh107Configuration;
import org.hibernate.cache.jcache.ConfigSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.JCacheManagerCustomizer;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.boot.info.GitProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.*;
import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.config.cache.PrefixedKeyGenerator;

@Configuration
@EnableCaching
public class CacheConfiguration {

    private GitProperties gitProperties;
    private BuildProperties buildProperties;
    private final javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration;

    public CacheConfiguration(JHipsterProperties jHipsterProperties) {
        JHipsterProperties.Cache.Ehcache ehcache = jHipsterProperties.getCache().getEhcache();

        jcacheConfiguration =
            Eh107Configuration.fromEhcacheCacheConfiguration(
                CacheConfigurationBuilder
                    .newCacheConfigurationBuilder(Object.class, Object.class, ResourcePoolsBuilder.heap(ehcache.getMaxEntries()))
                    .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(ehcache.getTimeToLiveSeconds())))
                    .build()
            );
    }

    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer(javax.cache.CacheManager cacheManager) {
        return hibernateProperties -> hibernateProperties.put(ConfigSettings.CACHE_MANAGER, cacheManager);
    }

    @Bean
    public JCacheManagerCustomizer cacheManagerCustomizer() {
        return cm -> {
            createCache(cm, com.techvg.pacs.repository.UserRepository.USERS_BY_LOGIN_CACHE);
            createCache(cm, com.techvg.pacs.repository.UserRepository.USERS_BY_EMAIL_CACHE);
            createCache(cm, com.techvg.pacs.domain.User.class.getName());
            createCache(cm, com.techvg.pacs.domain.Authority.class.getName());
            createCache(cm, com.techvg.pacs.domain.User.class.getName() + ".authorities");
            createCache(cm, com.techvg.pacs.domain.SecurityUser.class.getName());
            createCache(cm, com.techvg.pacs.domain.SecurityUser.class.getName() + ".securityPermissions");
            createCache(cm, com.techvg.pacs.domain.SecurityUser.class.getName() + ".securityRoles");
            createCache(cm, com.techvg.pacs.domain.SecurityRole.class.getName());
            createCache(cm, com.techvg.pacs.domain.SecurityRole.class.getName() + ".securityPermissions");
            createCache(cm, com.techvg.pacs.domain.SecurityRole.class.getName() + ".securityUsers");
            createCache(cm, com.techvg.pacs.domain.SecurityPermission.class.getName());
            createCache(cm, com.techvg.pacs.domain.SecurityPermission.class.getName() + ".securityRoles");
            createCache(cm, com.techvg.pacs.domain.SecurityPermission.class.getName() + ".securityUsers");
            createCache(cm, com.techvg.pacs.domain.Society.class.getName());
            createCache(cm, com.techvg.pacs.domain.AddressDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.State.class.getName());
            createCache(cm, com.techvg.pacs.domain.District.class.getName());
            createCache(cm, com.techvg.pacs.domain.Taluka.class.getName());
            createCache(cm, com.techvg.pacs.domain.Village.class.getName());
            createCache(cm, com.techvg.pacs.domain.ParameterLookup.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyAssets.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyBanksDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyAssetsData.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyConfig.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyExecutiveMember.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyPrerequisite.class.getName());
            createCache(cm, com.techvg.pacs.domain.LedgerAccounts.class.getName());
            createCache(cm, com.techvg.pacs.domain.AccountHeadCode.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyCropRegistration.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyNpaSetting.class.getName());
            createCache(cm, com.techvg.pacs.domain.ExpenditureType.class.getName());
            createCache(cm, com.techvg.pacs.domain.SchemesDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.SocietyLoanProduct.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanType.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanType.class.getName() + ".societyPrerequisites");
            createCache(cm, com.techvg.pacs.domain.LoanType.class.getName() + ".societyLoanProducts");
            createCache(cm, com.techvg.pacs.domain.LoanType.class.getName() + ".loanDemands");
            createCache(cm, com.techvg.pacs.domain.LoanType.class.getName() + ".loanDetails");
            createCache(cm, com.techvg.pacs.domain.LoanType.class.getName() + ".loanDisbursementDetails");
            createCache(cm, com.techvg.pacs.domain.Member.class.getName());
            createCache(cm, com.techvg.pacs.domain.Documents.class.getName());
            createCache(cm, com.techvg.pacs.domain.MemberBank.class.getName());
            createCache(cm, com.techvg.pacs.domain.MemberAssets.class.getName());
            createCache(cm, com.techvg.pacs.domain.MemberLandAssets.class.getName());
            createCache(cm, com.techvg.pacs.domain.IncomeDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.MemberExistingfacility.class.getName());
            createCache(cm, com.techvg.pacs.domain.EmployementDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.Nominee.class.getName());
            createCache(cm, com.techvg.pacs.domain.BankDhoranDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.MemberCropsDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanDemand.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.AmortizationDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanDisbursementDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanRepaymentDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.LoanWatapDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.Vouchers.class.getName());
            createCache(cm, com.techvg.pacs.domain.VoucherDetails.class.getName());
            createCache(cm, com.techvg.pacs.domain.VouchersHistory.class.getName());
            // jhipster-needle-ehcache-add-entry
        };
    }

    private void createCache(javax.cache.CacheManager cm, String cacheName) {
        javax.cache.Cache<Object, Object> cache = cm.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        } else {
            cm.createCache(cacheName, jcacheConfiguration);
        }
    }

    @Autowired(required = false)
    public void setGitProperties(GitProperties gitProperties) {
        this.gitProperties = gitProperties;
    }

    @Autowired(required = false)
    public void setBuildProperties(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @Bean
    public KeyGenerator keyGenerator() {
        return new PrefixedKeyGenerator(this.gitProperties, this.buildProperties);
    }
}
