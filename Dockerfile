# Multi-stage build for PACS Backend
FROM maven:3.8.6-eclipse-temurin-11 AS build

# Set working directory
WORKDIR /app

# Copy pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src
COPY package.json package-lock.json ./
COPY angular.json tsconfig.json tsconfig.app.json tsconfig.spec.json ./
COPY webpack ./webpack

# Build the application
RUN mvn clean package -Pprod -DskipTests

# Production stage
FROM eclipse-temurin:11-jre-focal

# Create app user
RUN groupadd -r app && useradd -r -g app app

# Set working directory
WORKDIR /app

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# Copy the built JAR file
COPY --from=build /app/target/*.jar app.jar

# Copy entrypoint script
COPY src/main/docker/jib/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Create directories for uploads and logs
RUN mkdir -p /app/uploads /app/logs && \
    chown -R app:app /app

# Switch to app user
USER app

# Expose port
EXPOSE 8191

# Set environment variables
ENV SPRING_OUTPUT_ANSI_ENABLED=ALWAYS \
    JHIPSTER_SLEEP=0 \
    JAVA_OPTS="-Xmx1g -Xms512m"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8191/management/health || exit 1

# Use entrypoint script
ENTRYPOINT ["/entrypoint.sh"]
