{"applications": "*", "changelogDate": "20220824183054", "dto": "mapstruct", "embedded": false, "entityTableName": "society_crop_registration", "fields": [{"fieldName": "cropName", "fieldType": "String"}, {"fieldName": "monthDuration", "fieldType": "Integer"}, {"fieldName": "season", "fieldType": "Season", "fieldValues": "KHARIP (Kharip),RABB<PERSON> (Rabbi),HANGAMI (Hangami)"}, {"fieldName": "cropLimit", "fieldType": "Double"}, {"fieldName": "year", "fieldType": "Integer"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "SocietyCropRegistration", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "society", "relationshipName": "society", "relationshipType": "many-to-one"}], "service": "serviceClass"}