{"applications": "*", "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "bank_dhoran_details", "fields": [{"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "code", "fieldType": "String"}, {"fieldName": "startDate", "fieldType": "Instant"}, {"fieldName": "endDate", "fieldType": "Instant"}, {"fieldName": "year", "fieldType": "String"}, {"fieldName": "url", "fieldType": "String"}, {"fieldName": "isActivate", "fieldType": "Boolean"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}, {"fieldName": "freeField4", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "BankDhoranDetails", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "society", "relationshipName": "society", "relationshipType": "many-to-one"}], "service": "serviceClass"}