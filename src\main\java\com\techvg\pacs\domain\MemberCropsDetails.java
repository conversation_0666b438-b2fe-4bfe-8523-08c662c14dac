package com.techvg.pacs.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.time.Instant;
import javax.persistence.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A MemberCropsDetails.
 */
@Entity
@Table(name = "member_crops_details")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class MemberCropsDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "season")
    private String season;

    @Column(name = "land_type")
    private String landType;

    @Column(name = "land_gatno")
    private String landGatno;

    @Column(name = "year")
    private Integer year;

    @Column(name = "land_area_in_hector")
    private Double landAreaInHector;

    @Column(name = "memberkmp_status")
    private Boolean memberkmpStatus;

    @Column(name = "society_kmp_status")
    private Boolean societyKmpStatus;

    @Column(name = "is_activate")
    private Boolean isActivate;

    @Column(name = "last_modified")
    private Instant lastModified;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "free_field_1")
    private String freeField1;

    @Column(name = "free_field_2")
    private String freeField2;

    @Column(name = "free_field_3")
    private String freeField3;

    @Column(name = "free_field_4")
    private String freeField4;

    @ManyToOne
    @JsonIgnoreProperties(value = { "society" }, allowSetters = true)
    private Member member;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public MemberCropsDetails id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSeason() {
        return this.season;
    }

    public MemberCropsDetails season(String season) {
        this.setSeason(season);
        return this;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getLandType() {
        return this.landType;
    }

    public MemberCropsDetails landType(String landType) {
        this.setLandType(landType);
        return this;
    }

    public void setLandType(String landType) {
        this.landType = landType;
    }

    public String getLandGatno() {
        return this.landGatno;
    }

    public MemberCropsDetails landGatno(String landGatno) {
        this.setLandGatno(landGatno);
        return this;
    }

    public void setLandGatno(String landGatno) {
        this.landGatno = landGatno;
    }

    public Integer getYear() {
        return this.year;
    }

    public MemberCropsDetails year(Integer year) {
        this.setYear(year);
        return this;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Double getLandAreaInHector() {
        return this.landAreaInHector;
    }

    public MemberCropsDetails landAreaInHector(Double landAreaInHector) {
        this.setLandAreaInHector(landAreaInHector);
        return this;
    }

    public void setLandAreaInHector(Double landAreaInHector) {
        this.landAreaInHector = landAreaInHector;
    }

    public Boolean getMemberkmpStatus() {
        return this.memberkmpStatus;
    }

    public MemberCropsDetails memberkmpStatus(Boolean memberkmpStatus) {
        this.setMemberkmpStatus(memberkmpStatus);
        return this;
    }

    public void setMemberkmpStatus(Boolean memberkmpStatus) {
        this.memberkmpStatus = memberkmpStatus;
    }

    public Boolean getSocietyKmpStatus() {
        return this.societyKmpStatus;
    }

    public MemberCropsDetails societyKmpStatus(Boolean societyKmpStatus) {
        this.setSocietyKmpStatus(societyKmpStatus);
        return this;
    }

    public void setSocietyKmpStatus(Boolean societyKmpStatus) {
        this.societyKmpStatus = societyKmpStatus;
    }

    public Boolean getIsActivate() {
        return this.isActivate;
    }

    public MemberCropsDetails isActivate(Boolean isActivate) {
        this.setIsActivate(isActivate);
        return this;
    }

    public void setIsActivate(Boolean isActivate) {
        this.isActivate = isActivate;
    }

    public Instant getLastModified() {
        return this.lastModified;
    }

    public MemberCropsDetails lastModified(Instant lastModified) {
        this.setLastModified(lastModified);
        return this;
    }

    public void setLastModified(Instant lastModified) {
        this.lastModified = lastModified;
    }

    public String getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    public MemberCropsDetails lastModifiedBy(String lastModifiedBy) {
        this.setLastModifiedBy(lastModifiedBy);
        return this;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public String getFreeField1() {
        return this.freeField1;
    }

    public MemberCropsDetails freeField1(String freeField1) {
        this.setFreeField1(freeField1);
        return this;
    }

    public void setFreeField1(String freeField1) {
        this.freeField1 = freeField1;
    }

    public String getFreeField2() {
        return this.freeField2;
    }

    public MemberCropsDetails freeField2(String freeField2) {
        this.setFreeField2(freeField2);
        return this;
    }

    public void setFreeField2(String freeField2) {
        this.freeField2 = freeField2;
    }

    public String getFreeField3() {
        return this.freeField3;
    }

    public MemberCropsDetails freeField3(String freeField3) {
        this.setFreeField3(freeField3);
        return this;
    }

    public void setFreeField3(String freeField3) {
        this.freeField3 = freeField3;
    }

    public String getFreeField4() {
        return this.freeField4;
    }

    public MemberCropsDetails freeField4(String freeField4) {
        this.setFreeField4(freeField4);
        return this;
    }

    public void setFreeField4(String freeField4) {
        this.freeField4 = freeField4;
    }

    public Member getMember() {
        return this.member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public MemberCropsDetails member(Member member) {
        this.setMember(member);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MemberCropsDetails)) {
            return false;
        }
        return id != null && id.equals(((MemberCropsDetails) o).id);
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MemberCropsDetails{" +
            "id=" + getId() +
            ", season='" + getSeason() + "'" +
            ", landType='" + getLandType() + "'" +
            ", landGatno='" + getLandGatno() + "'" +
            ", year=" + getYear() +
            ", landAreaInHector=" + getLandAreaInHector() +
            ", memberkmpStatus='" + getMemberkmpStatus() + "'" +
            ", societyKmpStatus='" + getSocietyKmpStatus() + "'" +
            ", isActivate='" + getIsActivate() + "'" +
            ", lastModified='" + getLastModified() + "'" +
            ", lastModifiedBy='" + getLastModifiedBy() + "'" +
            ", freeField1='" + getFreeField1() + "'" +
            ", freeField2='" + getFreeField2() + "'" +
            ", freeField3='" + getFreeField3() + "'" +
            ", freeField4='" + getFreeField4() + "'" +
            "}";
    }
}
