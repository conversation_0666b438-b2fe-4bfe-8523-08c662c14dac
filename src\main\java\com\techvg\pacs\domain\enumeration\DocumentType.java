package com.techvg.pacs.domain.enumeration;

/**
 * The DocumentType enumeration.
 */
public enum DocumentType {
    PROFILE_PICTURE("Profile_Picture"),
    SIGNATURE("Signature"),
    JINDAGI_PATRAK("Jindagi_Patrak"),
    EIGHT_A("Eight_A"),
    SAAT_BARA("Saat_Bara"),
    AADHAR("Aadhar_Card"),
    PAN_CARD("Pan_Card"),
    SOCIETY_LOGO("Society_logo"),
    LOGO_AG_LOAN("Logo_Ag_Loan"),
    LOGO_HOME_LOAN("Logo_Hm_Loan"),
    LOGO_VEH_LOAN("Logo_Veh_Loan"),
    LOGO_PER_LOAN("Logo_Per_Loan"),
    LOGO_GOLD_LOAN("Logo_Gold_Loan"),
    ASSET_DOC("Assets_Document"),
    MONTHLY_MEETING("Monthly_meeting"),
    MOM_file("MOM_File"),
    DHORAN_DOC("Dhoran_document"),
    GR_DOC("GR_Document"),
    OTHER("Other");

    private final String value;

    DocumentType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
