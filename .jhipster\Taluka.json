{"applications": "*", "changelogDate": "20220824183043", "dto": "mapstruct", "embedded": false, "entityTableName": "taluka", "fields": [{"fieldName": "talukaName", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "deleted", "fieldType": "Boolean"}, {"fieldName": "lgdCode", "fieldType": "<PERSON>"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Taluka", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceClass"}