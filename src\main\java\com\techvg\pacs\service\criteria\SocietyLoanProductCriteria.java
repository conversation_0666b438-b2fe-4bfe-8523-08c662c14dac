package com.techvg.pacs.service.criteria;

import java.io.Serializable;
import java.util.Objects;
import org.springdoc.api.annotations.ParameterObject;
import tech.jhipster.service.Criteria;
import tech.jhipster.service.filter.BooleanFilter;
import tech.jhipster.service.filter.DoubleFilter;
import tech.jhipster.service.filter.Filter;
import tech.jhipster.service.filter.FloatFilter;
import tech.jhipster.service.filter.InstantFilter;
import tech.jhipster.service.filter.IntegerFilter;
import tech.jhipster.service.filter.LongFilter;
import tech.jhipster.service.filter.StringFilter;

/**
 * Criteria class for the {@link com.techvg.pacs.domain.SocietyLoanProduct} entity. This class is used
 * in {@link com.techvg.pacs.web.rest.SocietyLoanProductResource} to receive all the possible filtering options from
 * the Http GET request parameters.
 * For example the following could be a valid request:
 * {@code /society-loan-products?id.greaterThan=5&attr1.contains=something&attr2.specified=false}
 * As <PERSON> is unable to properly convert the types, unless specific {@link Filter} class are used, we need to use
 * fix type specific filters.
 */
@ParameterObject
public class SocietyLoanProductCriteria implements Serializable, Criteria {

    private static final long serialVersionUID = 1L;

    private LongFilter id;

    private StringFilter productName;

    private StringFilter accHeadCode;

    private StringFilter parentAccHeadCode;

    private InstantFilter resolutionDate;

    private StringFilter resolutionNo;

    private StringFilter status;

    private LongFilter unitSize;

    private InstantFilter validFrom;

    private InstantFilter validTo;

    private DoubleFilter interestRate;

    private DoubleFilter maxLoanAmt;

    private DoubleFilter borrowingInterestRate;

    private DoubleFilter penaltyInterest;

    private DoubleFilter surcharge;

    private DoubleFilter loanDuration;

    private IntegerFilter numberOFInstallment;

    private DoubleFilter extendedInterstRate;

    private DoubleFilter centralGovSubsidyInterest;

    private DoubleFilter distBankSubsidyInterest;

    private DoubleFilter borrowerInterest;

    private DoubleFilter stateGovSubsidyInterest;

    private StringFilter year;

    private InstantFilter createdOn;

    private StringFilter createdBy;

    private BooleanFilter isActivate;

    private BooleanFilter isDeleted;

    private InstantFilter lastModified;

    private StringFilter lastModifiedBy;

    private StringFilter freeField1;

    private StringFilter freeField2;

    private StringFilter freeField3;

    private StringFilter freeField4;

    private LongFilter societyId;

    private LongFilter bankDhoranDetailsId;

    private LongFilter ledgerAccountsId;

    private LongFilter loanTypeId;

    private Boolean distinct;

    public SocietyLoanProductCriteria() {}

    public SocietyLoanProductCriteria(SocietyLoanProductCriteria other) {
        this.id = other.id == null ? null : other.id.copy();
        this.productName = other.productName == null ? null : other.productName.copy();
        this.accHeadCode = other.accHeadCode == null ? null : other.accHeadCode.copy();
        this.parentAccHeadCode = other.parentAccHeadCode == null ? null : other.parentAccHeadCode.copy();
        this.resolutionDate = other.resolutionDate == null ? null : other.resolutionDate.copy();
        this.resolutionNo = other.resolutionNo == null ? null : other.resolutionNo.copy();
        this.status = other.status == null ? null : other.status.copy();
        this.unitSize = other.unitSize == null ? null : other.unitSize.copy();
        this.validFrom = other.validFrom == null ? null : other.validFrom.copy();
        this.validTo = other.validTo == null ? null : other.validTo.copy();
        this.interestRate = other.interestRate == null ? null : other.interestRate.copy();
        this.maxLoanAmt = other.maxLoanAmt == null ? null : other.maxLoanAmt.copy();
        this.borrowingInterestRate = other.borrowingInterestRate == null ? null : other.borrowingInterestRate.copy();
        this.penaltyInterest = other.penaltyInterest == null ? null : other.penaltyInterest.copy();
        this.surcharge = other.surcharge == null ? null : other.surcharge.copy();
        this.loanDuration = other.loanDuration == null ? null : other.loanDuration.copy();
        this.numberOFInstallment = other.numberOFInstallment == null ? null : other.numberOFInstallment.copy();
        this.extendedInterstRate = other.extendedInterstRate == null ? null : other.extendedInterstRate.copy();
        this.centralGovSubsidyInterest = other.centralGovSubsidyInterest == null ? null : other.centralGovSubsidyInterest.copy();
        this.distBankSubsidyInterest = other.distBankSubsidyInterest == null ? null : other.distBankSubsidyInterest.copy();
        this.borrowerInterest = other.borrowerInterest == null ? null : other.borrowerInterest.copy();
        this.stateGovSubsidyInterest = other.stateGovSubsidyInterest == null ? null : other.stateGovSubsidyInterest.copy();
        this.year = other.year == null ? null : other.year.copy();
        this.createdOn = other.createdOn == null ? null : other.createdOn.copy();
        this.createdBy = other.createdBy == null ? null : other.createdBy.copy();
        this.isActivate = other.isActivate == null ? null : other.isActivate.copy();
        this.isDeleted = other.isDeleted == null ? null : other.isDeleted.copy();
        this.lastModified = other.lastModified == null ? null : other.lastModified.copy();
        this.lastModifiedBy = other.lastModifiedBy == null ? null : other.lastModifiedBy.copy();
        this.freeField1 = other.freeField1 == null ? null : other.freeField1.copy();
        this.freeField2 = other.freeField2 == null ? null : other.freeField2.copy();
        this.freeField3 = other.freeField3 == null ? null : other.freeField3.copy();
        this.freeField4 = other.freeField4 == null ? null : other.freeField4.copy();
        this.societyId = other.societyId == null ? null : other.societyId.copy();
        this.bankDhoranDetailsId = other.bankDhoranDetailsId == null ? null : other.bankDhoranDetailsId.copy();
        this.ledgerAccountsId = other.ledgerAccountsId == null ? null : other.ledgerAccountsId.copy();
        this.loanTypeId = other.loanTypeId == null ? null : other.loanTypeId.copy();
        this.distinct = other.distinct;
    }

    @Override
    public SocietyLoanProductCriteria copy() {
        return new SocietyLoanProductCriteria(this);
    }

    public LongFilter getId() {
        return id;
    }

    public LongFilter id() {
        if (id == null) {
            id = new LongFilter();
        }
        return id;
    }

    public void setId(LongFilter id) {
        this.id = id;
    }

    public StringFilter getProductName() {
        return productName;
    }

    public StringFilter productName() {
        if (productName == null) {
            productName = new StringFilter();
        }
        return productName;
    }

    public void setProductName(StringFilter productName) {
        this.productName = productName;
    }

    public StringFilter getAccHeadCode() {
        return accHeadCode;
    }

    public StringFilter accHeadCode() {
        if (accHeadCode == null) {
            accHeadCode = new StringFilter();
        }
        return accHeadCode;
    }

    public void setAccHeadCode(StringFilter accHeadCode) {
        this.accHeadCode = accHeadCode;
    }

    public StringFilter getParentAccHeadCode() {
        return parentAccHeadCode;
    }

    public StringFilter parentAccHeadCode() {
        if (parentAccHeadCode == null) {
            parentAccHeadCode = new StringFilter();
        }
        return parentAccHeadCode;
    }

    public void setParentAccHeadCode(StringFilter parentAccHeadCode) {
        this.parentAccHeadCode = parentAccHeadCode;
    }

    public InstantFilter getResolutionDate() {
        return resolutionDate;
    }

    public InstantFilter resolutionDate() {
        if (resolutionDate == null) {
            resolutionDate = new InstantFilter();
        }
        return resolutionDate;
    }

    public void setResolutionDate(InstantFilter resolutionDate) {
        this.resolutionDate = resolutionDate;
    }

    public StringFilter getResolutionNo() {
        return resolutionNo;
    }

    public StringFilter resolutionNo() {
        if (resolutionNo == null) {
            resolutionNo = new StringFilter();
        }
        return resolutionNo;
    }

    public void setResolutionNo(StringFilter resolutionNo) {
        this.resolutionNo = resolutionNo;
    }

    public StringFilter getStatus() {
        return status;
    }

    public StringFilter status() {
        if (status == null) {
            status = new StringFilter();
        }
        return status;
    }

    public void setStatus(StringFilter status) {
        this.status = status;
    }

    public LongFilter getUnitSize() {
        return unitSize;
    }

    public LongFilter unitSize() {
        if (unitSize == null) {
            unitSize = new LongFilter();
        }
        return unitSize;
    }

    public void setUnitSize(LongFilter unitSize) {
        this.unitSize = unitSize;
    }

    public InstantFilter getValidFrom() {
        return validFrom;
    }

    public InstantFilter validFrom() {
        if (validFrom == null) {
            validFrom = new InstantFilter();
        }
        return validFrom;
    }

    public void setValidFrom(InstantFilter validFrom) {
        this.validFrom = validFrom;
    }

    public InstantFilter getValidTo() {
        return validTo;
    }

    public InstantFilter validTo() {
        if (validTo == null) {
            validTo = new InstantFilter();
        }
        return validTo;
    }

    public void setValidTo(InstantFilter validTo) {
        this.validTo = validTo;
    }

    public DoubleFilter getInterestRate() {
        return interestRate;
    }

    public DoubleFilter interestRate() {
        if (interestRate == null) {
            interestRate = new DoubleFilter();
        }
        return interestRate;
    }

    public void setInterestRate(DoubleFilter interestRate) {
        this.interestRate = interestRate;
    }

    public DoubleFilter getMaxLoanAmt() {
        return maxLoanAmt;
    }

    public DoubleFilter maxLoanAmt() {
        if (maxLoanAmt == null) {
            maxLoanAmt = new DoubleFilter();
        }
        return maxLoanAmt;
    }

    public void setMaxLoanAmt(DoubleFilter maxLoanAmt) {
        this.maxLoanAmt = maxLoanAmt;
    }

    public DoubleFilter getBorrowingInterestRate() {
        return borrowingInterestRate;
    }

    public DoubleFilter borrowingInterestRate() {
        if (borrowingInterestRate == null) {
            borrowingInterestRate = new DoubleFilter();
        }
        return borrowingInterestRate;
    }

    public void setBorrowingInterestRate(DoubleFilter borrowingInterestRate) {
        this.borrowingInterestRate = borrowingInterestRate;
    }

    public DoubleFilter getPenaltyInterest() {
        return penaltyInterest;
    }

    public DoubleFilter penaltyInterest() {
        if (penaltyInterest == null) {
            penaltyInterest = new DoubleFilter();
        }
        return penaltyInterest;
    }

    public void setPenaltyInterest(DoubleFilter penaltyInterest) {
        this.penaltyInterest = penaltyInterest;
    }

    public DoubleFilter getSurcharge() {
        return surcharge;
    }

    public DoubleFilter surcharge() {
        if (surcharge == null) {
            surcharge = new DoubleFilter();
        }
        return surcharge;
    }

    public void setSurcharge(DoubleFilter surcharge) {
        this.surcharge = surcharge;
    }

    public DoubleFilter getLoanDuration() {
        return loanDuration;
    }

    public DoubleFilter loanDuration() {
        if (loanDuration == null) {
            loanDuration = new DoubleFilter();
        }
        return loanDuration;
    }

    public void setLoanDuration(DoubleFilter loanDuration) {
        this.loanDuration = loanDuration;
    }

    public IntegerFilter getNumberOFInstallment() {
        return numberOFInstallment;
    }

    public IntegerFilter numberOFInstallment() {
        if (numberOFInstallment == null) {
            numberOFInstallment = new IntegerFilter();
        }
        return numberOFInstallment;
    }

    public void setNumberOFInstallment(IntegerFilter numberOFInstallment) {
        this.numberOFInstallment = numberOFInstallment;
    }

    public DoubleFilter getExtendedInterstRate() {
        return extendedInterstRate;
    }

    public DoubleFilter extendedInterstRate() {
        if (extendedInterstRate == null) {
            extendedInterstRate = new DoubleFilter();
        }
        return extendedInterstRate;
    }

    public void setExtendedInterstRate(DoubleFilter extendedInterstRate) {
        this.extendedInterstRate = extendedInterstRate;
    }

    public DoubleFilter getCentralGovSubsidyInterest() {
        return centralGovSubsidyInterest;
    }

    public DoubleFilter centralGovSubsidyInterest() {
        if (centralGovSubsidyInterest == null) {
            centralGovSubsidyInterest = new DoubleFilter();
        }
        return centralGovSubsidyInterest;
    }

    public void setCentralGovSubsidyInterest(DoubleFilter centralGovSubsidyInterest) {
        this.centralGovSubsidyInterest = centralGovSubsidyInterest;
    }

    public DoubleFilter getDistBankSubsidyInterest() {
        return distBankSubsidyInterest;
    }

    public DoubleFilter distBankSubsidyInterest() {
        if (distBankSubsidyInterest == null) {
            distBankSubsidyInterest = new DoubleFilter();
        }
        return distBankSubsidyInterest;
    }

    public void setDistBankSubsidyInterest(DoubleFilter distBankSubsidyInterest) {
        this.distBankSubsidyInterest = distBankSubsidyInterest;
    }

    public DoubleFilter getBorrowerInterest() {
        return borrowerInterest;
    }

    public DoubleFilter borrowerInterest() {
        if (borrowerInterest == null) {
            borrowerInterest = new DoubleFilter();
        }
        return borrowerInterest;
    }

    public void setBorrowerInterest(DoubleFilter borrowerInterest) {
        this.borrowerInterest = borrowerInterest;
    }

    public DoubleFilter getStateGovSubsidyInterest() {
        return stateGovSubsidyInterest;
    }

    public DoubleFilter stateGovSubsidyInterest() {
        if (stateGovSubsidyInterest == null) {
            stateGovSubsidyInterest = new DoubleFilter();
        }
        return stateGovSubsidyInterest;
    }

    public void setStateGovSubsidyInterest(DoubleFilter stateGovSubsidyInterest) {
        this.stateGovSubsidyInterest = stateGovSubsidyInterest;
    }

    public StringFilter getYear() {
        return year;
    }

    public StringFilter year() {
        if (year == null) {
            year = new StringFilter();
        }
        return year;
    }

    public void setYear(StringFilter year) {
        this.year = year;
    }

    public InstantFilter getCreatedOn() {
        return createdOn;
    }

    public InstantFilter createdOn() {
        if (createdOn == null) {
            createdOn = new InstantFilter();
        }
        return createdOn;
    }

    public void setCreatedOn(InstantFilter createdOn) {
        this.createdOn = createdOn;
    }

    public StringFilter getCreatedBy() {
        return createdBy;
    }

    public StringFilter createdBy() {
        if (createdBy == null) {
            createdBy = new StringFilter();
        }
        return createdBy;
    }

    public void setCreatedBy(StringFilter createdBy) {
        this.createdBy = createdBy;
    }

    public BooleanFilter getIsActivate() {
        return isActivate;
    }

    public BooleanFilter isActivate() {
        if (isActivate == null) {
            isActivate = new BooleanFilter();
        }
        return isActivate;
    }

    public void setIsActivate(BooleanFilter isActivate) {
        this.isActivate = isActivate;
    }

    public BooleanFilter getIsDeleted() {
        return isDeleted;
    }

    public BooleanFilter isDeleted() {
        if (isDeleted == null) {
            isDeleted = new BooleanFilter();
        }
        return isDeleted;
    }

    public void setIsDeleted(BooleanFilter isDeleted) {
        this.isDeleted = isDeleted;
    }

    public InstantFilter getLastModified() {
        return lastModified;
    }

    public InstantFilter lastModified() {
        if (lastModified == null) {
            lastModified = new InstantFilter();
        }
        return lastModified;
    }

    public void setLastModified(InstantFilter lastModified) {
        this.lastModified = lastModified;
    }

    public StringFilter getLastModifiedBy() {
        return lastModifiedBy;
    }

    public StringFilter lastModifiedBy() {
        if (lastModifiedBy == null) {
            lastModifiedBy = new StringFilter();
        }
        return lastModifiedBy;
    }

    public void setLastModifiedBy(StringFilter lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public StringFilter getFreeField1() {
        return freeField1;
    }

    public StringFilter freeField1() {
        if (freeField1 == null) {
            freeField1 = new StringFilter();
        }
        return freeField1;
    }

    public void setFreeField1(StringFilter freeField1) {
        this.freeField1 = freeField1;
    }

    public StringFilter getFreeField2() {
        return freeField2;
    }

    public StringFilter freeField2() {
        if (freeField2 == null) {
            freeField2 = new StringFilter();
        }
        return freeField2;
    }

    public void setFreeField2(StringFilter freeField2) {
        this.freeField2 = freeField2;
    }

    public StringFilter getFreeField3() {
        return freeField3;
    }

    public StringFilter freeField3() {
        if (freeField3 == null) {
            freeField3 = new StringFilter();
        }
        return freeField3;
    }

    public void setFreeField3(StringFilter freeField3) {
        this.freeField3 = freeField3;
    }

    public StringFilter getFreeField4() {
        return freeField4;
    }

    public StringFilter freeField4() {
        if (freeField4 == null) {
            freeField4 = new StringFilter();
        }
        return freeField4;
    }

    public void setFreeField4(StringFilter freeField4) {
        this.freeField4 = freeField4;
    }

    public LongFilter getSocietyId() {
        return societyId;
    }

    public LongFilter societyId() {
        if (societyId == null) {
            societyId = new LongFilter();
        }
        return societyId;
    }

    public void setSocietyId(LongFilter societyId) {
        this.societyId = societyId;
    }

    public LongFilter getBankDhoranDetailsId() {
        return bankDhoranDetailsId;
    }

    public LongFilter bankDhoranDetailsId() {
        if (bankDhoranDetailsId == null) {
            bankDhoranDetailsId = new LongFilter();
        }
        return bankDhoranDetailsId;
    }

    public void setBankDhoranDetailsId(LongFilter bankDhoranDetailsId) {
        this.bankDhoranDetailsId = bankDhoranDetailsId;
    }

    public LongFilter getLedgerAccountsId() {
        return ledgerAccountsId;
    }

    public LongFilter ledgerAccountsId() {
        if (ledgerAccountsId == null) {
            ledgerAccountsId = new LongFilter();
        }
        return ledgerAccountsId;
    }

    public void setLedgerAccountsId(LongFilter ledgerAccountsId) {
        this.ledgerAccountsId = ledgerAccountsId;
    }

    public LongFilter getLoanTypeId() {
        return loanTypeId;
    }

    public LongFilter loanTypeId() {
        if (loanTypeId == null) {
            loanTypeId = new LongFilter();
        }
        return loanTypeId;
    }

    public void setLoanTypeId(LongFilter loanTypeId) {
        this.loanTypeId = loanTypeId;
    }

    public Boolean getDistinct() {
        return distinct;
    }

    public void setDistinct(Boolean distinct) {
        this.distinct = distinct;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final SocietyLoanProductCriteria that = (SocietyLoanProductCriteria) o;
        return (
            Objects.equals(id, that.id) &&
            Objects.equals(productName, that.productName) &&
            Objects.equals(accHeadCode, that.accHeadCode) &&
            Objects.equals(parentAccHeadCode, that.parentAccHeadCode) &&
            Objects.equals(resolutionDate, that.resolutionDate) &&
            Objects.equals(resolutionNo, that.resolutionNo) &&
            Objects.equals(status, that.status) &&
            Objects.equals(unitSize, that.unitSize) &&
            Objects.equals(validFrom, that.validFrom) &&
            Objects.equals(validTo, that.validTo) &&
            Objects.equals(interestRate, that.interestRate) &&
            Objects.equals(maxLoanAmt, that.maxLoanAmt) &&
            Objects.equals(borrowingInterestRate, that.borrowingInterestRate) &&
            Objects.equals(penaltyInterest, that.penaltyInterest) &&
            Objects.equals(surcharge, that.surcharge) &&
            Objects.equals(loanDuration, that.loanDuration) &&
            Objects.equals(numberOFInstallment, that.numberOFInstallment) &&
            Objects.equals(extendedInterstRate, that.extendedInterstRate) &&
            Objects.equals(centralGovSubsidyInterest, that.centralGovSubsidyInterest) &&
            Objects.equals(distBankSubsidyInterest, that.distBankSubsidyInterest) &&
            Objects.equals(borrowerInterest, that.borrowerInterest) &&
            Objects.equals(stateGovSubsidyInterest, that.stateGovSubsidyInterest) &&
            Objects.equals(year, that.year) &&
            Objects.equals(createdOn, that.createdOn) &&
            Objects.equals(createdBy, that.createdBy) &&
            Objects.equals(isActivate, that.isActivate) &&
            Objects.equals(isDeleted, that.isDeleted) &&
            Objects.equals(lastModified, that.lastModified) &&
            Objects.equals(lastModifiedBy, that.lastModifiedBy) &&
            Objects.equals(freeField1, that.freeField1) &&
            Objects.equals(freeField2, that.freeField2) &&
            Objects.equals(freeField3, that.freeField3) &&
            Objects.equals(freeField4, that.freeField4) &&
            Objects.equals(societyId, that.societyId) &&
            Objects.equals(bankDhoranDetailsId, that.bankDhoranDetailsId) &&
            Objects.equals(ledgerAccountsId, that.ledgerAccountsId) &&
            Objects.equals(loanTypeId, that.loanTypeId) &&
            Objects.equals(distinct, that.distinct)
        );
    }

    @Override
    public int hashCode() {
        return Objects.hash(
            id,
            productName,
            accHeadCode,
            parentAccHeadCode,
            resolutionDate,
            resolutionNo,
            status,
            unitSize,
            validFrom,
            validTo,
            interestRate,
            maxLoanAmt,
            borrowingInterestRate,
            penaltyInterest,
            surcharge,
            loanDuration,
            numberOFInstallment,
            extendedInterstRate,
            centralGovSubsidyInterest,
            distBankSubsidyInterest,
            borrowerInterest,
            stateGovSubsidyInterest,
            year,
            createdOn,
            createdBy,
            isActivate,
            isDeleted,
            lastModified,
            lastModifiedBy,
            freeField1,
            freeField2,
            freeField3,
            freeField4,
            societyId,
            bankDhoranDetailsId,
            ledgerAccountsId,
            loanTypeId,
            distinct
        );
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "SocietyLoanProductCriteria{" +
            (id != null ? "id=" + id + ", " : "") +
            (productName != null ? "productName=" + productName + ", " : "") +
            (accHeadCode != null ? "accHeadCode=" + accHeadCode + ", " : "") +
            (parentAccHeadCode != null ? "parentAccHeadCode=" + parentAccHeadCode + ", " : "") +
            (resolutionDate != null ? "resolutionDate=" + resolutionDate + ", " : "") +
            (resolutionNo != null ? "resolutionNo=" + resolutionNo + ", " : "") +
            (status != null ? "status=" + status + ", " : "") +
            (unitSize != null ? "unitSize=" + unitSize + ", " : "") +
            (validFrom != null ? "validFrom=" + validFrom + ", " : "") +
            (validTo != null ? "validTo=" + validTo + ", " : "") +
            (interestRate != null ? "interestRate=" + interestRate + ", " : "") +
            (maxLoanAmt != null ? "maxLoanAmt=" + maxLoanAmt + ", " : "") +
            (borrowingInterestRate != null ? "borrowingInterestRate=" + borrowingInterestRate + ", " : "") +
            (penaltyInterest != null ? "penaltyInterest=" + penaltyInterest + ", " : "") +
            (surcharge != null ? "surcharge=" + surcharge + ", " : "") +
            (loanDuration != null ? "loanDuration=" + loanDuration + ", " : "") +
            (numberOFInstallment != null ? "numberOFInstallment=" + numberOFInstallment + ", " : "") +
            (extendedInterstRate != null ? "extendedInterstRate=" + extendedInterstRate + ", " : "") +
            (centralGovSubsidyInterest != null ? "centralGovSubsidyInterest=" + centralGovSubsidyInterest + ", " : "") +
            (distBankSubsidyInterest != null ? "distBankSubsidyInterest=" + distBankSubsidyInterest + ", " : "") +
            (borrowerInterest != null ? "borrowerInterest=" + borrowerInterest + ", " : "") +
            (stateGovSubsidyInterest != null ? "stateGovSubsidyInterest=" + stateGovSubsidyInterest + ", " : "") +
            (year != null ? "year=" + year + ", " : "") +
            (createdOn != null ? "createdOn=" + createdOn + ", " : "") +
            (createdBy != null ? "createdBy=" + createdBy + ", " : "") +
            (isActivate != null ? "isActivate=" + isActivate + ", " : "") +
            (isDeleted != null ? "isDeleted=" + isDeleted + ", " : "") +
            (lastModified != null ? "lastModified=" + lastModified + ", " : "") +
            (lastModifiedBy != null ? "lastModifiedBy=" + lastModifiedBy + ", " : "") +
            (freeField1 != null ? "freeField1=" + freeField1 + ", " : "") +
            (freeField2 != null ? "freeField2=" + freeField2 + ", " : "") +
            (freeField3 != null ? "freeField3=" + freeField3 + ", " : "") +
            (freeField4 != null ? "freeField4=" + freeField4 + ", " : "") +
            (societyId != null ? "societyId=" + societyId + ", " : "") +
            (bankDhoranDetailsId != null ? "bankDhoranDetailsId=" + bankDhoranDetailsId + ", " : "") +
            (ledgerAccountsId != null ? "ledgerAccountsId=" + ledgerAccountsId + ", " : "") +
            (loanTypeId != null ? "loanTypeId=" + loanTypeId + ", " : "") +
            (distinct != null ? "distinct=" + distinct + ", " : "") +
            "}";
    }
}
