{"applications": "*", "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "member_bank", "fields": [{"fieldName": "bankName", "fieldType": "String"}, {"fieldName": "branchName", "fieldType": "String"}, {"fieldName": "accountNumber", "fieldType": "<PERSON>", "fieldValidateRules": ["unique"]}, {"fieldName": "ifsccode", "fieldType": "String"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "MemberBank", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "member", "relationshipName": "member", "relationshipType": "many-to-one"}], "service": "serviceClass"}