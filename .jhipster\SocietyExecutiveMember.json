{"applications": "*", "changelogDate": "20220824183050", "dto": "mapstruct", "embedded": false, "entityTableName": "society_executive_member", "fields": [{"fieldName": "designation", "fieldType": "String"}, {"fieldName": "elected<PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "electedto", "fieldType": "Instant"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "SocietyExecutiveMember", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "member", "ownerSide": true, "relationshipName": "member", "relationshipType": "one-to-one"}], "service": "serviceClass"}