{"applications": "*", "changelogDate": "20220824183111", "dto": "mapstruct", "embedded": false, "entityTableName": "loan_demand", "fields": [{"fieldName": "loanDemandAmount", "fieldType": "Double"}, {"fieldName": "maxAllowedAmount", "fieldType": "Double"}, {"fieldName": "adjusted<PERSON>emand", "fieldType": "Double"}, {"fieldName": "annualIncome", "fieldType": "Double"}, {"fieldName": "costOfInvestment", "fieldType": "Double"}, {"fieldName": "demandedLandAreaInHector", "fieldType": "Integer"}, {"fieldName": "status", "fieldType": "LoanStatus", "fieldValues": "RECORDED (Recorded),APPLIED (Applied),PENDING (Pending),AWAITED (Awaited),CHART_GENRATED (Chart_Genrated),APPROVED (Approved),REJECTED (Rejected),CANCELLED (cancelled),DISBURSED (Disbursed),ACTIVE (Active),CLOSED (Closed)"}, {"fieldName": "seasonOfCropLoan", "fieldType": "String"}, {"fieldName": "loanSteps", "fieldType": "StepperNumber", "fieldValues": "STEP_1 (Step_1),STEP_2 (Step_2),STEP_3 (Step_3),STEP_4 (Step_4),STEP_5 (Step_5),STEP_6 (Step_6)"}, {"fieldName": "year", "fieldType": "String"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}, {"fieldName": "freeField4", "fieldType": "String"}, {"fieldName": "freeField5", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "member", "relationshipName": "member", "relationshipType": "many-to-one"}, {"otherEntityName": "societyLoanProduct", "relationshipName": "societyLoanProduct", "relationshipType": "many-to-one"}, {"otherEntityName": "memberLandAssets", "relationshipName": "memberLandAssets", "relationshipType": "many-to-one"}, {"otherEntityName": "societyCropRegistration", "relationshipName": "societyCropRegistration", "relationshipType": "many-to-one"}, {"otherEntityName": "loanType", "otherEntityRelationshipName": "loanDemand", "relationshipName": "loanType", "relationshipType": "many-to-one"}], "service": "serviceClass"}