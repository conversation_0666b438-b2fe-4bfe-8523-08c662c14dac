{"applications": "*", "changelogDate": "20220824183049", "dto": "mapstruct", "embedded": false, "entityTableName": "society_config", "fields": [{"fieldName": "config<PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "configType", "fieldType": "String"}, {"fieldName": "status", "fieldType": "String"}, {"fieldName": "value", "fieldType": "Double"}, {"fieldName": "year", "fieldType": "String"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "SocietyConfig", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "society", "relationshipName": "society", "relationshipType": "many-to-one"}, {"otherEntityName": "bankDhoranDetails", "relationshipName": "bankDhoranDetails", "relationshipType": "many-to-one"}], "service": "serviceClass"}