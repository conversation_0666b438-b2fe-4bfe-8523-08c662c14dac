{"applications": "*", "changelogDate": "20220824183044", "dto": "mapstruct", "embedded": false, "entityTableName": "village", "fields": [{"fieldName": "villageName", "fieldType": "String", "fieldValidateRules": ["required"]}, {"fieldName": "deleted", "fieldType": "Boolean"}, {"fieldName": "lgdCode", "fieldType": "<PERSON>"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "Village", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceClass"}