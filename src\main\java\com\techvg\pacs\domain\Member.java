package com.techvg.pacs.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.techvg.pacs.domain.enumeration.Gender;
import com.techvg.pacs.domain.enumeration.LoanStatus;
import com.techvg.pacs.domain.enumeration.MaritalStatus;
import com.techvg.pacs.domain.enumeration.ResidentalStatus;
import com.techvg.pacs.domain.enumeration.Status;
import com.techvg.pacs.domain.enumeration.StepperNumber;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import javax.persistence.*;
import javax.validation.constraints.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Member.
 */
@Entity
@Table(name = "member")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class Member implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "middle_name")
    private String middleName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "member_unique_id", unique = true)
    private String memberUniqueId;

    @Column(name = "father_name")
    private String fatherName;

    @Column(name = "mother_name")
    private String motherName;

    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private Gender gender;

    @Column(name = "dob")
    private LocalDate dob;

    @Column(name = "email")
    private String email;

    @Column(name = "mobile_no")
    private String mobileNo;

    @Column(name = "religion")
    private String religion;

    @Column(name = "category")
    private String category;

    @Column(name = "cast")
    private String cast;

    @Column(name = "aadhar_card_no", unique = true)
    private String aadharCardNo;

    @Column(name = "pan_card_no", unique = true)
    private String panCardNo;

    @Column(name = "passport_no", unique = true)
    private String passportNo;

    @Column(name = "passport_expiry")
    private String passportExpiry;

    @Column(name = "ration_card")
    private String rationCard;

    @Enumerated(EnumType.STRING)
    @Column(name = "residental_status")
    private ResidentalStatus residentalStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "marital_status")
    private MaritalStatus maritalStatus;

    @Column(name = "family_member_count")
    private Long familyMemberCount;

    @Column(name = "occupation")
    private String occupation;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "no_of_dependents")
    private Long noOfDependents;

    @Column(name = "application_date")
    private Instant applicationDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private Status status;

    @Column(name = "board_resolution_no")
    private String boardResolutionNo;

    @Column(name = "board_resolution_date")
    private LocalDate boardResolutionDate;

    @Column(name = "highest_qualification")
    private String highestQualification;

    @Column(name = "has_adhar_card_verified")
    private Boolean hasAdharCardVerified;

    @Column(name = "has_pan_card_verified")
    private Boolean hasPanCardVerified;

    @Enumerated(EnumType.STRING)
    @Column(name = "loan_status")
    private LoanStatus loanStatus;

    @Column(name = "member_type")
    private String memberType;

    @Column(name = "is_active")
    private Boolean isActive;

    @Column(name = "is_deleted")
    private Boolean isDeleted;

    @Enumerated(EnumType.STRING)
    @Column(name = "profile_stepper")
    private StepperNumber profileStepper;

    @Column(name = "last_modified")
    private Instant lastModified;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_on")
    private Instant createdOn;

    @Column(name = "free_field_1")
    private String freeField1;

    @Column(name = "free_field_2")
    private String freeField2;

    @Column(name = "free_field_3")
    private String freeField3;

    @Column(name = "free_field_4")
    private String freeField4;

    @ManyToOne
    @JsonIgnoreProperties(value = { "addressDetails", "society" }, allowSetters = true)
    private Society society;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Member id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return this.firstName;
    }

    public Member firstName(String firstName) {
        this.setFirstName(firstName);
        return this;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleName() {
        return this.middleName;
    }

    public Member middleName(String middleName) {
        this.setMiddleName(middleName);
        return this;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getLastName() {
        return this.lastName;
    }

    public Member lastName(String lastName) {
        this.setLastName(lastName);
        return this;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMemberUniqueId() {
        return this.memberUniqueId;
    }

    public Member memberUniqueId(String memberUniqueId) {
        this.setMemberUniqueId(memberUniqueId);
        return this;
    }

    public void setMemberUniqueId(String memberUniqueId) {
        this.memberUniqueId = memberUniqueId;
    }

    public String getFatherName() {
        return this.fatherName;
    }

    public Member fatherName(String fatherName) {
        this.setFatherName(fatherName);
        return this;
    }

    public void setFatherName(String fatherName) {
        this.fatherName = fatherName;
    }

    public String getMotherName() {
        return this.motherName;
    }

    public Member motherName(String motherName) {
        this.setMotherName(motherName);
        return this;
    }

    public void setMotherName(String motherName) {
        this.motherName = motherName;
    }

    public Gender getGender() {
        return this.gender;
    }

    public Member gender(Gender gender) {
        this.setGender(gender);
        return this;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public LocalDate getDob() {
        return this.dob;
    }

    public Member dob(LocalDate dob) {
        this.setDob(dob);
        return this;
    }

    public void setDob(LocalDate dob) {
        this.dob = dob;
    }

    public String getEmail() {
        return this.email;
    }

    public Member email(String email) {
        this.setEmail(email);
        return this;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getMobileNo() {
        return this.mobileNo;
    }

    public Member mobileNo(String mobileNo) {
        this.setMobileNo(mobileNo);
        return this;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getReligion() {
        return this.religion;
    }

    public Member religion(String religion) {
        this.setReligion(religion);
        return this;
    }

    public void setReligion(String religion) {
        this.religion = religion;
    }

    public String getCategory() {
        return this.category;
    }

    public Member category(String category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCast() {
        return this.cast;
    }

    public Member cast(String cast) {
        this.setCast(cast);
        return this;
    }

    public void setCast(String cast) {
        this.cast = cast;
    }

    public String getAadharCardNo() {
        return this.aadharCardNo;
    }

    public Member aadharCardNo(String aadharCardNo) {
        this.setAadharCardNo(aadharCardNo);
        return this;
    }

    public void setAadharCardNo(String aadharCardNo) {
        this.aadharCardNo = aadharCardNo;
    }

    public String getPanCardNo() {
        return this.panCardNo;
    }

    public Member panCardNo(String panCardNo) {
        this.setPanCardNo(panCardNo);
        return this;
    }

    public void setPanCardNo(String panCardNo) {
        this.panCardNo = panCardNo;
    }

    public String getPassportNo() {
        return this.passportNo;
    }

    public Member passportNo(String passportNo) {
        this.setPassportNo(passportNo);
        return this;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public String getPassportExpiry() {
        return this.passportExpiry;
    }

    public Member passportExpiry(String passportExpiry) {
        this.setPassportExpiry(passportExpiry);
        return this;
    }

    public void setPassportExpiry(String passportExpiry) {
        this.passportExpiry = passportExpiry;
    }

    public String getRationCard() {
        return this.rationCard;
    }

    public Member rationCard(String rationCard) {
        this.setRationCard(rationCard);
        return this;
    }

    public void setRationCard(String rationCard) {
        this.rationCard = rationCard;
    }

    public ResidentalStatus getResidentalStatus() {
        return this.residentalStatus;
    }

    public Member residentalStatus(ResidentalStatus residentalStatus) {
        this.setResidentalStatus(residentalStatus);
        return this;
    }

    public void setResidentalStatus(ResidentalStatus residentalStatus) {
        this.residentalStatus = residentalStatus;
    }

    public MaritalStatus getMaritalStatus() {
        return this.maritalStatus;
    }

    public Member maritalStatus(MaritalStatus maritalStatus) {
        this.setMaritalStatus(maritalStatus);
        return this;
    }

    public void setMaritalStatus(MaritalStatus maritalStatus) {
        this.maritalStatus = maritalStatus;
    }

    public Long getFamilyMemberCount() {
        return this.familyMemberCount;
    }

    public Member familyMemberCount(Long familyMemberCount) {
        this.setFamilyMemberCount(familyMemberCount);
        return this;
    }

    public void setFamilyMemberCount(Long familyMemberCount) {
        this.familyMemberCount = familyMemberCount;
    }

    public String getOccupation() {
        return this.occupation;
    }

    public Member occupation(String occupation) {
        this.setOccupation(occupation);
        return this;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getNationality() {
        return this.nationality;
    }

    public Member nationality(String nationality) {
        this.setNationality(nationality);
        return this;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public Long getNoOfDependents() {
        return this.noOfDependents;
    }

    public Member noOfDependents(Long noOfDependents) {
        this.setNoOfDependents(noOfDependents);
        return this;
    }

    public void setNoOfDependents(Long noOfDependents) {
        this.noOfDependents = noOfDependents;
    }

    public Instant getApplicationDate() {
        return this.applicationDate;
    }

    public Member applicationDate(Instant applicationDate) {
        this.setApplicationDate(applicationDate);
        return this;
    }

    public void setApplicationDate(Instant applicationDate) {
        this.applicationDate = applicationDate;
    }

    public Status getStatus() {
        return this.status;
    }

    public Member status(Status status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getBoardResolutionNo() {
        return this.boardResolutionNo;
    }

    public Member boardResolutionNo(String boardResolutionNo) {
        this.setBoardResolutionNo(boardResolutionNo);
        return this;
    }

    public void setBoardResolutionNo(String boardResolutionNo) {
        this.boardResolutionNo = boardResolutionNo;
    }

    public LocalDate getBoardResolutionDate() {
        return this.boardResolutionDate;
    }

    public Member boardResolutionDate(LocalDate boardResolutionDate) {
        this.setBoardResolutionDate(boardResolutionDate);
        return this;
    }

    public void setBoardResolutionDate(LocalDate boardResolutionDate) {
        this.boardResolutionDate = boardResolutionDate;
    }

    public String getHighestQualification() {
        return this.highestQualification;
    }

    public Member highestQualification(String highestQualification) {
        this.setHighestQualification(highestQualification);
        return this;
    }

    public void setHighestQualification(String highestQualification) {
        this.highestQualification = highestQualification;
    }

    public Boolean getHasAdharCardVerified() {
        return this.hasAdharCardVerified;
    }

    public Member hasAdharCardVerified(Boolean hasAdharCardVerified) {
        this.setHasAdharCardVerified(hasAdharCardVerified);
        return this;
    }

    public void setHasAdharCardVerified(Boolean hasAdharCardVerified) {
        this.hasAdharCardVerified = hasAdharCardVerified;
    }

    public Boolean getHasPanCardVerified() {
        return this.hasPanCardVerified;
    }

    public Member hasPanCardVerified(Boolean hasPanCardVerified) {
        this.setHasPanCardVerified(hasPanCardVerified);
        return this;
    }

    public void setHasPanCardVerified(Boolean hasPanCardVerified) {
        this.hasPanCardVerified = hasPanCardVerified;
    }

    public LoanStatus getLoanStatus() {
        return this.loanStatus;
    }

    public Member loanStatus(LoanStatus loanStatus) {
        this.setLoanStatus(loanStatus);
        return this;
    }

    public void setLoanStatus(LoanStatus loanStatus) {
        this.loanStatus = loanStatus;
    }

    public String getMemberType() {
        return this.memberType;
    }

    public Member memberType(String memberType) {
        this.setMemberType(memberType);
        return this;
    }

    public void setMemberType(String memberType) {
        this.memberType = memberType;
    }

    public Boolean getIsActive() {
        return this.isActive;
    }

    public Member isActive(Boolean isActive) {
        this.setIsActive(isActive);
        return this;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public Member isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public StepperNumber getProfileStepper() {
        return this.profileStepper;
    }

    public Member profileStepper(StepperNumber profileStepper) {
        this.setProfileStepper(profileStepper);
        return this;
    }

    public void setProfileStepper(StepperNumber profileStepper) {
        this.profileStepper = profileStepper;
    }

    public Instant getLastModified() {
        return this.lastModified;
    }

    public Member lastModified(Instant lastModified) {
        this.setLastModified(lastModified);
        return this;
    }

    public void setLastModified(Instant lastModified) {
        this.lastModified = lastModified;
    }

    public String getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    public Member lastModifiedBy(String lastModifiedBy) {
        this.setLastModifiedBy(lastModifiedBy);
        return this;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public Member createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedOn() {
        return this.createdOn;
    }

    public Member createdOn(Instant createdOn) {
        this.setCreatedOn(createdOn);
        return this;
    }

    public void setCreatedOn(Instant createdOn) {
        this.createdOn = createdOn;
    }

    public String getFreeField1() {
        return this.freeField1;
    }

    public Member freeField1(String freeField1) {
        this.setFreeField1(freeField1);
        return this;
    }

    public void setFreeField1(String freeField1) {
        this.freeField1 = freeField1;
    }

    public String getFreeField2() {
        return this.freeField2;
    }

    public Member freeField2(String freeField2) {
        this.setFreeField2(freeField2);
        return this;
    }

    public void setFreeField2(String freeField2) {
        this.freeField2 = freeField2;
    }

    public String getFreeField3() {
        return this.freeField3;
    }

    public Member freeField3(String freeField3) {
        this.setFreeField3(freeField3);
        return this;
    }

    public void setFreeField3(String freeField3) {
        this.freeField3 = freeField3;
    }

    public String getFreeField4() {
        return this.freeField4;
    }

    public Member freeField4(String freeField4) {
        this.setFreeField4(freeField4);
        return this;
    }

    public void setFreeField4(String freeField4) {
        this.freeField4 = freeField4;
    }

    public Society getSociety() {
        return this.society;
    }

    public void setSociety(Society society) {
        this.society = society;
    }

    public Member society(Society society) {
        this.setSociety(society);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Member)) {
            return false;
        }
        return id != null && id.equals(((Member) o).id);
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Member{" +
            "id=" + getId() +
            ", firstName='" + getFirstName() + "'" +
            ", middleName='" + getMiddleName() + "'" +
            ", lastName='" + getLastName() + "'" +
            ", memberUniqueId='" + getMemberUniqueId() + "'" +
            ", fatherName='" + getFatherName() + "'" +
            ", motherName='" + getMotherName() + "'" +
            ", gender='" + getGender() + "'" +
            ", dob='" + getDob() + "'" +
            ", email='" + getEmail() + "'" +
            ", mobileNo='" + getMobileNo() + "'" +
            ", religion='" + getReligion() + "'" +
            ", category='" + getCategory() + "'" +
            ", cast='" + getCast() + "'" +
            ", aadharCardNo='" + getAadharCardNo() + "'" +
            ", panCardNo='" + getPanCardNo() + "'" +
            ", passportNo='" + getPassportNo() + "'" +
            ", passportExpiry='" + getPassportExpiry() + "'" +
            ", rationCard='" + getRationCard() + "'" +
            ", residentalStatus='" + getResidentalStatus() + "'" +
            ", maritalStatus='" + getMaritalStatus() + "'" +
            ", familyMemberCount=" + getFamilyMemberCount() +
            ", occupation='" + getOccupation() + "'" +
            ", nationality='" + getNationality() + "'" +
            ", noOfDependents=" + getNoOfDependents() +
            ", applicationDate='" + getApplicationDate() + "'" +
            ", status='" + getStatus() + "'" +
            ", boardResolutionNo='" + getBoardResolutionNo() + "'" +
            ", boardResolutionDate='" + getBoardResolutionDate() + "'" +
            ", highestQualification='" + getHighestQualification() + "'" +
            ", hasAdharCardVerified='" + getHasAdharCardVerified() + "'" +
            ", hasPanCardVerified='" + getHasPanCardVerified() + "'" +
            ", loanStatus='" + getLoanStatus() + "'" +
            ", memberType='" + getMemberType() + "'" +
            ", isActive='" + getIsActive() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", profileStepper='" + getProfileStepper() + "'" +
            ", lastModified='" + getLastModified() + "'" +
            ", lastModifiedBy='" + getLastModifiedBy() + "'" +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdOn='" + getCreatedOn() + "'" +
            ", freeField1='" + getFreeField1() + "'" +
            ", freeField2='" + getFreeField2() + "'" +
            ", freeField3='" + getFreeField3() + "'" +
            ", freeField4='" + getFreeField4() + "'" +
            "}";
    }
}
