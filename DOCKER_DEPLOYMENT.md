# PACS Backend Docker Deployment Guide

This guide provides instructions for deploying the PACS Backend application using Docker Compose on Ubuntu Server.

## Prerequisites

- Ubuntu Server 18.04+ 
- Docker and Docker Compose installed
- At least 4GB RAM and 20GB disk space
- Sudo access

## Quick Start

1. **Run the setup script:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

2. **Build the application image:**
   ```bash
   mvn clean package jib:dockerBuild
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   nano .env  # Edit passwords and other settings
   ```

4. **Deploy the application:**
   ```bash
   ./deploy.sh
   ```

## Directory Structure

The application uses `/srv/pacs` as the base directory with the following structure:

```
/srv/pacs/
├── mysql/
│   ├── data/     # MySQL data files (persistent storage)
│   ├── config/   # MySQL configuration files
│   └── logs/     # MySQL log files
├── uploads/      # Application file uploads
├── logs/         # Application log files
└── backups/      # Database and file backups
```

## Services

### PACS Backend Application
- **Container:** `pacs-backend`
- **Image:** `pacsbackend:latest`
- **Port:** `8191` (mapped to host)
- **Health Check:** `http://localhost:8191/management/health`

### MySQL Database
- **Container:** `pacs-mysql`
- **Image:** `mysql:8.0.29`
- **Port:** `3306` (localhost only)
- **Database:** `pacs`
- **User:** `pacs`

## Configuration

### Environment Variables

Key environment variables that can be customized:

- `MYSQL_PASSWORD`: Database password for pacs user
- `MYSQL_ROOT_PASSWORD`: Database root password
- `JAVA_OPTS`: JVM options (default: -Xmx2g -Xms1g)
- `SPRING_PROFILES_ACTIVE`: Spring profiles (default: prod,api-docs)

### Memory Configuration

The default configuration allocates:
- **Application:** 2GB max heap, 1GB initial heap
- **MySQL:** 1GB buffer pool size

Adjust these values in `docker-compose.yml` based on your server resources.

## Management Commands

### Start Services
```bash
docker-compose up -d
```

### Stop Services
```bash
docker-compose down
```

### Restart Services
```bash
docker-compose restart
```

### View Logs
```bash
# Application logs
docker-compose logs -f pacs-backend

# Database logs
docker-compose logs -f pacs-mysql

# All logs
docker-compose logs -f
```

### Check Service Status
```bash
docker-compose ps
```

## Backup and Restore

### Create Backup
```bash
./backup.sh
```

This creates:
- Database dump: `/srv/pacs/backups/pacs_db_YYYYMMDD_HHMMSS.sql`
- Uploads archive: `/srv/pacs/backups/pacs_uploads_YYYYMMDD_HHMMSS.tar.gz`

### Restore Database
```bash
# Copy backup file to container and restore
docker-compose exec -T pacs-mysql mysql -u pacs -p pacs < /srv/pacs/backups/pacs_db_YYYYMMDD_HHMMSS.sql
```

### Restore Uploads
```bash
# Extract uploads backup
sudo tar -xzf /srv/pacs/backups/pacs_uploads_YYYYMMDD_HHMMSS.tar.gz -C /srv/pacs/
```

## Monitoring

### Health Checks
- **Application:** `http://localhost:8191/management/health`
- **Metrics:** `http://localhost:8191/management/metrics`
- **Info:** `http://localhost:8191/management/info`

### Log Files
- **Application logs:** `/srv/pacs/logs/`
- **MySQL error log:** `/srv/pacs/mysql/logs/error.log`
- **MySQL general log:** `/srv/pacs/mysql/logs/general.log`
- **MySQL slow query log:** `/srv/pacs/mysql/logs/slow.log`

## Security Considerations

1. **Change default passwords** in `.env` file
2. **Firewall configuration:**
   ```bash
   sudo ufw allow 8191/tcp  # Application port
   sudo ufw deny 3306/tcp   # Block external MySQL access
   ```
3. **SSL/TLS:** Consider using a reverse proxy (nginx) with SSL certificates
4. **Regular updates:** Keep Docker images updated

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   sudo netstat -tulpn | grep :8191
   sudo netstat -tulpn | grep :3306
   ```

2. **Permission issues:**
   ```bash
   sudo chown -R 1000:1000 /srv/pacs/uploads
   sudo chown -R 1000:1000 /srv/pacs/logs
   sudo chown -R 999:999 /srv/pacs/mysql
   ```

3. **Database connection issues:**
   ```bash
   docker-compose logs pacs-mysql
   docker-compose exec pacs-mysql mysql -u pacs -p
   ```

4. **Application startup issues:**
   ```bash
   docker-compose logs pacs-backend
   ```

### Performance Tuning

1. **Increase MySQL buffer pool:**
   Edit `/srv/pacs/mysql/config/my.cnf` and restart MySQL container

2. **Adjust JVM heap size:**
   Modify `_JAVA_OPTIONS` in `docker-compose.yml`

3. **Monitor resource usage:**
   ```bash
   docker stats
   ```

## Maintenance

### Regular Tasks

1. **Monitor disk space:**
   ```bash
   df -h /srv/pacs
   ```

2. **Rotate logs:**
   ```bash
   docker-compose exec pacs-mysql mysqladmin flush-logs
   ```

3. **Update images:**
   ```bash
   docker-compose pull
   docker-compose up -d
   ```

4. **Clean up old images:**
   ```bash
   docker image prune -f
   ```

## Support

For issues related to:
- **Application:** Check application logs and JHipster documentation
- **Database:** Check MySQL logs and MySQL 8.0 documentation
- **Docker:** Check Docker and Docker Compose documentation
