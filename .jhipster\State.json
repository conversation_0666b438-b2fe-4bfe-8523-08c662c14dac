{"applications": "*", "changelogDate": "20220824183041", "dto": "mapstruct", "embedded": false, "entityTableName": "state", "fields": [{"fieldName": "stateName", "fieldType": "String", "fieldValidateRules": ["required", "unique"]}, {"fieldName": "deleted", "fieldType": "Boolean"}, {"fieldName": "lgdCode", "fieldType": "<PERSON>"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "State", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceClass"}