version: '3.8'

services:
  pacs-backend:
    image: pacsbackend:latest
    container_name: pacs-backend
    restart: unless-stopped
    depends_on:
      - pacs-mysql
    environment:
      - _JAVA_OPTIONS=-Xmx2g -Xms1g
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - SPRING_DATASOURCE_URL=******************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=pacs
      - SPRING_DATASOURCE_PASSWORD=pacsSecurePassword123!
      - SPRING_LIQUIBASE_URL=******************************************************************************************************************************************************************
      - SPRING_LIQUIBASE_USER=pacs
      - SPRING_LIQUIBASE_PASSWORD=pacsSecurePassword123!
      - JHIPSTER_SLEEP=30
      - UPLOAD_PATH=/app/uploads
    ports:
      - "8191:8191"
    volumes:
      - /srv/pacs/uploads:/app/uploads
      - /srv/pacs/logs:/app/logs
    networks:
      - pacs-network

  pacs-mysql:
    image: mysql:8.0.29
    container_name: pacs-mysql
    restart: unless-stopped
    environment:
      - MYSQL_DATABASE=pacs
      - MYSQL_USER=pacs
      - MYSQL_PASSWORD=pacsSecurePassword123!
      - MYSQL_ROOT_PASSWORD=rootSecurePassword123!
    ports:
      - "3306:3306"
    volumes:
      - /srv/pacs/mysql/data:/var/lib/mysql
      - /srv/pacs/mysql/config:/etc/mysql/conf.d
    networks:
      - pacs-network
    command: >
      mysqld 
      --lower_case_table_names=1 
      --skip-ssl 
      --character_set_server=utf8mb4 
      --explicit_defaults_for_timestamp

networks:
  pacs-network:
    driver: bridge
