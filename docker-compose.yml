version: '3.8'

services:
  pacs-backend:
    image: pacsbackend:latest
    container_name: pacs-backend
    restart: unless-stopped
    depends_on:
      - pacs-mysql
    environment:
      - _JAVA_OPTIONS=-Xmx2g -Xms1g
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - SPRING_DATASOURCE_URL=******************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=pacs
      - SPRING_DATASOURCE_PASSWORD=pacsSecurePassword123!
      - SPRING_LIQUIBASE_URL=******************************************************************************************************************************************************************
      - SPRING_LIQUIBASE_USER=pacs
      - SPRING_LIQUIBASE_PASSWORD=pacsSecurePassword123!
      - JHIPSTER_SLEEP=30
      - MANAGEMENT_METRICS_EXPORT_PROMETHEUS_ENABLED=true
      - UPLOAD_PATH=/app/uploads
    ports:
      - "8191:8191"
    volumes:
      - /srv/pacs/uploads:/app/uploads
      - /srv/pacs/logs:/app/logs
    networks:
      - pacs-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8191/management/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  pacs-mysql:
    image: mysql:8.0.29
    container_name: pacs-mysql
    restart: unless-stopped
    environment:
      - MYSQL_DATABASE=pacs
      - MYSQL_USER=pacs
      - MYSQL_PASSWORD=pacsSecurePassword123!
      - MYSQL_ROOT_PASSWORD=rootSecurePassword123!
    ports:
      - "127.0.0.1:3306:3306"
    volumes:
      - /srv/pacs/mysql/data:/var/lib/mysql
      - /srv/pacs/mysql/config:/etc/mysql/conf.d
      - /srv/pacs/mysql/logs:/var/log/mysql
    networks:
      - pacs-network
    command: >
      mysqld 
      --lower_case_table_names=1 
      --skip-ssl 
      --character_set_server=utf8mb4 
      --explicit_defaults_for_timestamp
      --log-error=/var/log/mysql/error.log
      --general-log=1
      --general-log-file=/var/log/mysql/general.log
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long_query_time=2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "pacs", "-ppacsSecurePassword123!"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  pacs-network:
    driver: bridge

volumes:
  mysql-data:
    driver: local
  mysql-config:
    driver: local
  mysql-logs:
    driver: local
  app-uploads:
    driver: local
  app-logs:
    driver: local
