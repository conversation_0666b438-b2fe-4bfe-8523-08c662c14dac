package com.techvg.pacs.config;

import static java.net.URLDecoder.decode;

import java.io.File;
import java.nio.file.Paths;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import org.gaul.modernizer_maven_annotations.SuppressModernizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.server.WebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import tech.jhipster.config.JHipsterProperties;

/**
 * Configuration of web application with Servlet 3.0 APIs.
 */
@Configuration
public class WebConfigurer implements ServletContextInitializer, WebServerFactoryCustomizer<WebServerFactory> {

	private final Logger log = LoggerFactory.getLogger(WebConfigurer.class);

	private final Environment env;

	private final JHipsterProperties jHipsterProperties;

	public WebConfigurer(Environment env, JHipsterProperties jHipsterProperties) {
		this.env = env;
		this.jHipsterProperties = jHipsterProperties;
	}

	@Override
	public void onStartup(ServletContext servletContext) throws ServletException {
		if (env.getActiveProfiles().length != 0) {
			log.info("Web application configuration, using profiles: {}", (Object[]) env.getActiveProfiles());
		}

		log.info("Web application fully configured");
	}

	/**
	 * Customize the Servlet engine: Mime types, the document root, the cache.
	 */
	@Override
	public void customize(WebServerFactory server) {
		// When running in an IDE or with ./mvnw spring-boot:run, set location of the
		// static web assets.
		try {
			setLocationForStaticAssets(server);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private void setLocationForStaticAssets(WebServerFactory server) throws Exception {
		if (server instanceof ConfigurableServletWebServerFactory) {
			ConfigurableServletWebServerFactory servletWebServer = (ConfigurableServletWebServerFactory) server;
			File root;
			String prefixPath = resolvePathPrefix();
			root = new File(prefixPath + "target/classes/static/");
			if (root.exists() && root.isDirectory()) {
				servletWebServer.setDocumentRoot(root);
			}
		}
	}

	/**
	 * Resolve path prefix to static resources.
	 * 
	 * @throws Exception
	 */
	@SuppressModernizer
	private String resolvePathPrefix() throws Exception {
		// String fullExecutablePath = decode(this.getClass().getResource("").getPath(),
		// StandardCharsets.UTF_8);
		String fullExecutablePath = decode(this.getClass().getResource("").getPath().toString(), "UTF_8");
		String rootPath = Paths.get(".").toUri().normalize().getPath();
		String extractedPath = fullExecutablePath.replace(rootPath, "");
		int extractionEndIndex = extractedPath.indexOf("target/");
		if (extractionEndIndex <= 0) {
			return "";
		}
		return extractedPath.substring(0, extractionEndIndex);
	}

	@Bean
	public CorsFilter corsFilter() {
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		CorsConfiguration config = jHipsterProperties.getCors();
		if (!CollectionUtils.isEmpty(config.getAllowedOrigins())
				|| !CollectionUtils.isEmpty(config.getAllowedOriginPatterns())) {
			log.debug("Registering CORS filter");
			source.registerCorsConfiguration("/api/image/**", config);
			source.registerCorsConfiguration("/api/uploads/files/**", config);
			source.registerCorsConfiguration("/image/**", config);
			source.registerCorsConfiguration("/management/**", config);
			source.registerCorsConfiguration("/v3/api-docs", config);
			source.registerCorsConfiguration("/swagger-ui/**", config);
		}
		return new CorsFilter(source);
	}
}
