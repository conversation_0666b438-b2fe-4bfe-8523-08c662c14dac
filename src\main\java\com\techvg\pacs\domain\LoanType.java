package com.techvg.pacs.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import javax.persistence.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A LoanType.
 */
@Entity
@Table(name = "loan_type")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class LoanType implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "loan_name")
    private String loanName;

    @Column(name = "decription")
    private String decription;

    @Column(name = "value")
    private String value;

    @Column(name = "code")
    private String code;

    @Column(name = "offer")
    private String offer;

    @Column(name = "last_modified")
    private Instant lastModified;

    @Column(name = "last_modified_by")
    private String lastModifiedBy;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "created_on")
    private Instant createdOn;

    @Column(name = "is_deleted")
    private Boolean isDeleted;

    @Column(name = "free_field_1")
    private String freeField1;

    @Column(name = "free_field_2")
    private String freeField2;

    @Column(name = "free_field_3")
    private String freeField3;

    @OneToMany(mappedBy = "loanType")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "society", "loanType" }, allowSetters = true)
    private Set<SocietyPrerequisite> societyPrerequisites = new HashSet<>();

    @OneToMany(mappedBy = "loanType")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "society", "bankDhoranDetails", "ledgerAccounts", "loanType" }, allowSetters = true)
    private Set<SocietyLoanProduct> societyLoanProducts = new HashSet<>();

    @OneToMany(mappedBy = "loanType")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(
        value = { "member", "societyLoanProduct", "memberLandAssets", "societyCropRegistration", "loanType" },
        allowSetters = true
    )
    private Set<LoanDemand> loanDemands = new HashSet<>();

    @OneToMany(mappedBy = "loanType")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(
        value = { "loanDemand", "member", "societyCropRegistration", "societyLoanProduct", "bankDhoranDetails", "loanType" },
        allowSetters = true
    )
    private Set<LoanDetails> loanDetails = new HashSet<>();

    @OneToMany(mappedBy = "loanType")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "loanDetails", "loanType" }, allowSetters = true)
    private Set<LoanDisbursementDetails> loanDisbursementDetails = new HashSet<>();

    @ManyToOne
    @JsonIgnoreProperties(value = { "addressDetails", "society" }, allowSetters = true)
    private Society society;

    @ManyToOne
    @JsonIgnoreProperties(
        value = {
            "societyPrerequisites", "societyLoanProducts", "loanDemands", "loanDetails", "loanDisbursementDetails", "society", "loanType",
        },
        allowSetters = true
    )
    private LoanType loanType;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public LoanType id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoanName() {
        return this.loanName;
    }

    public LoanType loanName(String loanName) {
        this.setLoanName(loanName);
        return this;
    }

    public void setLoanName(String loanName) {
        this.loanName = loanName;
    }

    public String getDecription() {
        return this.decription;
    }

    public LoanType decription(String decription) {
        this.setDecription(decription);
        return this;
    }

    public void setDecription(String decription) {
        this.decription = decription;
    }

    public String getValue() {
        return this.value;
    }

    public LoanType value(String value) {
        this.setValue(value);
        return this;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCode() {
        return this.code;
    }

    public LoanType code(String code) {
        this.setCode(code);
        return this;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOffer() {
        return this.offer;
    }

    public LoanType offer(String offer) {
        this.setOffer(offer);
        return this;
    }

    public void setOffer(String offer) {
        this.offer = offer;
    }

    public Instant getLastModified() {
        return this.lastModified;
    }

    public LoanType lastModified(Instant lastModified) {
        this.setLastModified(lastModified);
        return this;
    }

    public void setLastModified(Instant lastModified) {
        this.lastModified = lastModified;
    }

    public String getLastModifiedBy() {
        return this.lastModifiedBy;
    }

    public LoanType lastModifiedBy(String lastModifiedBy) {
        this.setLastModifiedBy(lastModifiedBy);
        return this;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public LoanType createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedOn() {
        return this.createdOn;
    }

    public LoanType createdOn(Instant createdOn) {
        this.setCreatedOn(createdOn);
        return this;
    }

    public void setCreatedOn(Instant createdOn) {
        this.createdOn = createdOn;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public LoanType isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getFreeField1() {
        return this.freeField1;
    }

    public LoanType freeField1(String freeField1) {
        this.setFreeField1(freeField1);
        return this;
    }

    public void setFreeField1(String freeField1) {
        this.freeField1 = freeField1;
    }

    public String getFreeField2() {
        return this.freeField2;
    }

    public LoanType freeField2(String freeField2) {
        this.setFreeField2(freeField2);
        return this;
    }

    public void setFreeField2(String freeField2) {
        this.freeField2 = freeField2;
    }

    public String getFreeField3() {
        return this.freeField3;
    }

    public LoanType freeField3(String freeField3) {
        this.setFreeField3(freeField3);
        return this;
    }

    public void setFreeField3(String freeField3) {
        this.freeField3 = freeField3;
    }

    public Set<SocietyPrerequisite> getSocietyPrerequisites() {
        return this.societyPrerequisites;
    }

    public void setSocietyPrerequisites(Set<SocietyPrerequisite> societyPrerequisites) {
        if (this.societyPrerequisites != null) {
            this.societyPrerequisites.forEach(i -> i.setLoanType(null));
        }
        if (societyPrerequisites != null) {
            societyPrerequisites.forEach(i -> i.setLoanType(this));
        }
        this.societyPrerequisites = societyPrerequisites;
    }

    public LoanType societyPrerequisites(Set<SocietyPrerequisite> societyPrerequisites) {
        this.setSocietyPrerequisites(societyPrerequisites);
        return this;
    }

    public LoanType addSocietyPrerequisite(SocietyPrerequisite societyPrerequisite) {
        this.societyPrerequisites.add(societyPrerequisite);
        societyPrerequisite.setLoanType(this);
        return this;
    }

    public LoanType removeSocietyPrerequisite(SocietyPrerequisite societyPrerequisite) {
        this.societyPrerequisites.remove(societyPrerequisite);
        societyPrerequisite.setLoanType(null);
        return this;
    }

    public Set<SocietyLoanProduct> getSocietyLoanProducts() {
        return this.societyLoanProducts;
    }

    public void setSocietyLoanProducts(Set<SocietyLoanProduct> societyLoanProducts) {
        if (this.societyLoanProducts != null) {
            this.societyLoanProducts.forEach(i -> i.setLoanType(null));
        }
        if (societyLoanProducts != null) {
            societyLoanProducts.forEach(i -> i.setLoanType(this));
        }
        this.societyLoanProducts = societyLoanProducts;
    }

    public LoanType societyLoanProducts(Set<SocietyLoanProduct> societyLoanProducts) {
        this.setSocietyLoanProducts(societyLoanProducts);
        return this;
    }

    public LoanType addSocietyLoanProduct(SocietyLoanProduct societyLoanProduct) {
        this.societyLoanProducts.add(societyLoanProduct);
        societyLoanProduct.setLoanType(this);
        return this;
    }

    public LoanType removeSocietyLoanProduct(SocietyLoanProduct societyLoanProduct) {
        this.societyLoanProducts.remove(societyLoanProduct);
        societyLoanProduct.setLoanType(null);
        return this;
    }

    public Set<LoanDemand> getLoanDemands() {
        return this.loanDemands;
    }

    public void setLoanDemands(Set<LoanDemand> loanDemands) {
        if (this.loanDemands != null) {
            this.loanDemands.forEach(i -> i.setLoanType(null));
        }
        if (loanDemands != null) {
            loanDemands.forEach(i -> i.setLoanType(this));
        }
        this.loanDemands = loanDemands;
    }

    public LoanType loanDemands(Set<LoanDemand> loanDemands) {
        this.setLoanDemands(loanDemands);
        return this;
    }

    public LoanType addLoanDemand(LoanDemand loanDemand) {
        this.loanDemands.add(loanDemand);
        loanDemand.setLoanType(this);
        return this;
    }

    public LoanType removeLoanDemand(LoanDemand loanDemand) {
        this.loanDemands.remove(loanDemand);
        loanDemand.setLoanType(null);
        return this;
    }

    public Set<LoanDetails> getLoanDetails() {
        return this.loanDetails;
    }

    public void setLoanDetails(Set<LoanDetails> loanDetails) {
        if (this.loanDetails != null) {
            this.loanDetails.forEach(i -> i.setLoanType(null));
        }
        if (loanDetails != null) {
            loanDetails.forEach(i -> i.setLoanType(this));
        }
        this.loanDetails = loanDetails;
    }

    public LoanType loanDetails(Set<LoanDetails> loanDetails) {
        this.setLoanDetails(loanDetails);
        return this;
    }

    public LoanType addLoanDetails(LoanDetails loanDetails) {
        this.loanDetails.add(loanDetails);
        loanDetails.setLoanType(this);
        return this;
    }

    public LoanType removeLoanDetails(LoanDetails loanDetails) {
        this.loanDetails.remove(loanDetails);
        loanDetails.setLoanType(null);
        return this;
    }

    public Set<LoanDisbursementDetails> getLoanDisbursementDetails() {
        return this.loanDisbursementDetails;
    }

    public void setLoanDisbursementDetails(Set<LoanDisbursementDetails> loanDisbursementDetails) {
        if (this.loanDisbursementDetails != null) {
            this.loanDisbursementDetails.forEach(i -> i.setLoanType(null));
        }
        if (loanDisbursementDetails != null) {
            loanDisbursementDetails.forEach(i -> i.setLoanType(this));
        }
        this.loanDisbursementDetails = loanDisbursementDetails;
    }

    public LoanType loanDisbursementDetails(Set<LoanDisbursementDetails> loanDisbursementDetails) {
        this.setLoanDisbursementDetails(loanDisbursementDetails);
        return this;
    }

    public LoanType addLoanDisbursementDetails(LoanDisbursementDetails loanDisbursementDetails) {
        this.loanDisbursementDetails.add(loanDisbursementDetails);
        loanDisbursementDetails.setLoanType(this);
        return this;
    }

    public LoanType removeLoanDisbursementDetails(LoanDisbursementDetails loanDisbursementDetails) {
        this.loanDisbursementDetails.remove(loanDisbursementDetails);
        loanDisbursementDetails.setLoanType(null);
        return this;
    }

    public Society getSociety() {
        return this.society;
    }

    public void setSociety(Society society) {
        this.society = society;
    }

    public LoanType society(Society society) {
        this.setSociety(society);
        return this;
    }

    public LoanType getLoanType() {
        return this.loanType;
    }

    public void setLoanType(LoanType loanType) {
        this.loanType = loanType;
    }

    public LoanType loanType(LoanType loanType) {
        this.setLoanType(loanType);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LoanType)) {
            return false;
        }
        return id != null && id.equals(((LoanType) o).id);
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "LoanType{" +
            "id=" + getId() +
            ", loanName='" + getLoanName() + "'" +
            ", decription='" + getDecription() + "'" +
            ", value='" + getValue() + "'" +
            ", code='" + getCode() + "'" +
            ", offer='" + getOffer() + "'" +
            ", lastModified='" + getLastModified() + "'" +
            ", lastModifiedBy='" + getLastModifiedBy() + "'" +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdOn='" + getCreatedOn() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", freeField1='" + getFreeField1() + "'" +
            ", freeField2='" + getFreeField2() + "'" +
            ", freeField3='" + getFreeField3() + "'" +
            "}";
    }
}
