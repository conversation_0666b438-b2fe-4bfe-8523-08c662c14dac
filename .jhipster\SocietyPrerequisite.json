{"applications": "*", "changelogDate": "20220824183051", "dto": "mapstruct", "embedded": false, "entityTableName": "society_prerequisite", "fields": [{"fieldName": "docType", "fieldType": "String"}, {"fieldName": "documentDesc", "fieldType": "String"}, {"fieldName": "documentName", "fieldType": "String"}, {"fieldName": "mandatory", "fieldType": "String"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "created<PERSON>y", "fieldType": "String"}, {"fieldName": "createdOn", "fieldType": "Instant"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "SocietyPrerequisite", "pagination": "pagination", "readOnly": false, "relationships": [{"otherEntityName": "society", "relationshipName": "society", "relationshipType": "many-to-one"}, {"otherEntityName": "loanType", "otherEntityRelationshipName": "societyPrerequisite", "relationshipName": "loanType", "relationshipType": "many-to-one"}], "service": "serviceClass"}