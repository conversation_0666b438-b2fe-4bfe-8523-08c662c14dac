{"applications": "*", "changelogDate": "**************", "dto": "mapstruct", "embedded": false, "entityTableName": "voucher_details", "fields": [{"fieldName": "accHeadCode", "fieldType": "String"}, {"fieldName": "creditAccount", "fieldType": "String"}, {"fieldName": "debitAccount", "fieldType": "String"}, {"fieldName": "transferAmt", "fieldType": "Double"}, {"fieldName": "isDeleted", "fieldType": "Boolean"}, {"fieldName": "lastModified", "fieldType": "Instant"}, {"fieldName": "lastModifiedBy", "fieldType": "String"}, {"fieldName": "freeField1", "fieldType": "String"}, {"fieldName": "freeField2", "fieldType": "String"}, {"fieldName": "freeField3", "fieldType": "String"}, {"fieldName": "freeField4", "fieldType": "String"}], "fluentMethods": true, "jpaMetamodelFiltering": true, "name": "VoucherDetails", "pagination": "pagination", "readOnly": false, "relationships": [], "service": "serviceClass"}